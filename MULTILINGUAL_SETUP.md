# 🌍 Многоязычный лендинг VBflare

## Настроена поддержка 3 языков:

- **🇷🇺 Русский** (по умолчанию) - `/ru`
- **🇺🇸 Английский** - `/en` 
- **🇺🇿 Узбекский** - `/uz`

## 🚀 Как запустить:

1. Установите зависимости:
```bash
npm install
```

2. Запустите сервер разработки:
```bash
npm run dev
```

3. Откройте в браузере:
- http://localhost:3000 (автоматически перенаправит на `/ru`)
- http://localhost:3000/ru (русский)
- http://localhost:3000/en (английский)
- http://localhost:3000/uz (узбекский)

## 🎯 Особенности:

### ✅ Что работает:
- **Переключение языков** - кнопки RU/EN/UZ в навигации
- **Автоматический редирект** - корневая страница перенаправляет на `/ru`
- **Полная локализация** - все тексты переведены
- **SEO-оптимизация** - правильные meta-теги для каждого языка
- **Плавная прокрутка** - якорные ссылки работают на всех языках
- **Функциональные формы** - отправка email на всех языках

### 🎨 Дизайн:
- Современный градиентный дизайн
- Стеклянные эффекты (backdrop-blur)
- Анимации и переходы
- Адаптивная верстка
- Темная тема

### 📱 Секции:
1. **Hero** - главный экран с призывом к действию
2. **О компании** - миссия и статистика
3. **Услуги** - детальное описание сервисов
4. **Команда** - профили Elyor, Dilshod, Shahriyor
5. **Контакты** - форма обратной связи и контакты
6. **Футер** - дополнительные ссылки

### 🔧 Технологии:
- **Next.js 15** - React фреймворк
- **next-intl** - интернационализация
- **Tailwind CSS** - стилизация
- **TypeScript** - типизация

## 📁 Структура файлов:

```
src/
├── app/
│   ├── [locale]/          # Локализованные страницы
│   │   ├── layout.tsx     # Layout для каждого языка
│   │   └── page.tsx       # Главная страница
│   ├── globals.css        # Глобальные стили
│   ├── layout.tsx         # Корневой layout
│   └── page.tsx           # Редирект на /ru
├── i18n/
│   └── routing.ts         # Конфигурация маршрутизации
├── i18n.ts               # Конфигурация next-intl
└── middleware.ts         # Middleware для локалей

messages/
├── ru.json               # Русские переводы
├── en.json               # Английские переводы
└── uz.json               # Узбекские переводы
```

## 🎯 Как добавить новый язык:

1. Создайте файл перевода: `messages/новый_язык.json`
2. Добавьте локаль в `src/middleware.ts`
3. Добавьте локаль в `src/i18n/routing.ts`
4. Добавьте кнопку переключения в компонент `LanguageSwitcher`

## 📞 Контакты VBflare:

- **Email:** <EMAIL>
- **Телефон:** +************
- **Адрес:** Тошкент, ул Махтумкули

---

**Создано с ❤️ командой VBflare**
